package dev.deepseek;

import dev.deepseek.commands.CommandHandler;
import dev.deepseek.config.ConfigManager;
import dev.deepseek.config.DragManager;
import dev.deepseek.event.EventProtocol;
import dev.deepseek.intent.api.account.IntentAccount;
import dev.deepseek.intent.cloud.CloudDataManager;
import dev.deepseek.module.Module;
import dev.deepseek.module.ModuleCollection;
import dev.deepseek.scripting.api.ScriptManager;
import dev.deepseek.ui.altmanager.GuiAltManager;
import dev.deepseek.ui.altmanager.helpers.KingGenApi;
import dev.deepseek.ui.searchbar.SearchBar;
import dev.deepseek.ui.sidegui.SideGUI;
import dev.deepseek.utils.Utils;
import dev.deepseek.utils.client.ReleaseType;
import dev.deepseek.utils.misc.DiscordRPC;
import dev.deepseek.utils.objects.DiscordAccount;
import dev.deepseek.utils.objects.Dragging;
import dev.deepseek.utils.server.PingerUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.awt.*;
import java.io.File;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Getter
@Setter
public class DeepSeek implements Utils {

    public static final DeepSeek INSTANCE = new DeepSeek();

    public static final String NAME = "DeepSeek";
    public static final String VERSION = "5.1";
    public static final ReleaseType RELEASE = ReleaseType.DEV;
    public static final Logger LOGGER = LogManager.getLogger(NAME);
    public static final File DIRECTORY = new File(mc.mcDataDir, NAME);

    private final EventProtocol eventProtocol = new EventProtocol();
    private final CloudDataManager cloudDataManager = new CloudDataManager();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final SideGUI sideGui = new SideGUI();
    private final SearchBar searchBar = new SearchBar();
    private ModuleCollection moduleCollection;
    private ScriptManager scriptManager;
    private IntentAccount intentAccount;
    private ConfigManager configManager;
    private GuiAltManager altManager;
    private CommandHandler commandHandler;
    private PingerUtils pingerUtils;
    private DiscordRPC discordRPC;
    public KingGenApi kingGenApi;
    private DiscordAccount discordAccount;

    public static boolean updateGuiScale;
    public static int prevGuiScale;

    public String getVersion() {
        return VERSION + (RELEASE != ReleaseType.PUBLIC ? " (" + RELEASE.getName() + ")" : "");
    }

    public final Color getClientColor() {
        return new Color(236, 133, 209);
    }

    public final Color getAlternateClientColor() {
        return new Color(28, 167, 222);
    }

    public boolean isEnabled(Class<? extends Module> c) {
        Module m = INSTANCE.moduleCollection.get(c);
        return m != null && m.isEnabled();
    }

    public Dragging createDrag(Module module, String name, float x, float y) {
        DragManager.draggables.put(name, new Dragging(module, name, x, y));
        return DragManager.draggables.get(name);
    }

}
