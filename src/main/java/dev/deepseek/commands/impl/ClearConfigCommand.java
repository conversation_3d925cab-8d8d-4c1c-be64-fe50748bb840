package dev.deepseek.commands.impl;

import dev.deepseek.Tenacity;
import dev.deepseek.commands.Command;
import dev.deepseek.module.Module;

public class ClearConfigCommand extends Command {

    public ClearConfigCommand() {
        super("clearconfig", "Turns off all enabled modules", ".clearconfig");
    }

    @Override
    public void execute(String[] args) {
        DeepSeek.INSTANCE.getModuleCollection().getModules().stream().filter(Module::isEnabled).forEach(Module::toggle);
    }
}
