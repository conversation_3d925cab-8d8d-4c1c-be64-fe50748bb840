package dev.deepseek.module.impl.combat;

import dev.deepseek.event.impl.player.KeepSprintEvent;
import dev.deepseek.module.Category;
import dev.deepseek.module.Module;

public final class KeepSprint extends Module {

    public KeepSprint() {
        super("KeepSprint", Category.COMBAT, "Stops sprint reset after hitting");
    }

    @Override
    public void onKeepSprintEvent(KeepSprintEvent event) {
        event.cancel();
    }

}
