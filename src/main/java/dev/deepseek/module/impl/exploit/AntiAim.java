package dev.tenacity.module.impl.exploit;

import dev.tenacity.event.impl.game.TickEvent;
import dev.tenacity.event.impl.player.UpdateEvent;
import dev.tenacity.module.Category;
import dev.tenacity.module.Module;
import dev.tenacity.module.settings.impl.BooleanSetting;
import net.minecraft.network.play.client.C03PacketPlayer;

public class AntiAim extends Module {

    private BooleanSetting rollPosition = new BooleanSetting("Roll Position", true);


    @Override
    public void onUpdateEvent(UpdateEvent event) {
        double posx = mc.thePlayer.posX;
        double posy = mc.thePlayer.posY;
        double posz = mc.thePlayer.posZ;
        mc.getNetHandler().addToSendQueue(new C03PacketPlayer.C04PacketPlayerPosition(
                mc.thePlayer.posX + 15, mc.thePlayer.posY, mc.thePlayer.posZ, false
        ));
        mc.thePlayer.setPosition(posx, posy, posz);
    }

    public AntiAim() {
        super("AntiAim", Category.EXPLOIT, "CSGO-like AntiAim");
    }
}
