package dev.tenacity.module.impl.exploit;

import dev.tenacity.event.impl.network.PacketSendEvent;
import dev.tenacity.event.impl.player.MotionEvent;
import dev.tenacity.module.Category;
import dev.tenacity.module.Module;
import dev.tenacity.module.impl.combat.KillAura;
import dev.tenacity.module.settings.impl.ModeSetting;
import dev.tenacity.utils.server.PacketUtils;
import dev.tenacity.utils.time.TimerUtil;
import net.minecraft.network.Packet;
import net.minecraft.network.play.client.C03PacketPlayer;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

public final class AntiAura extends Module {

    private final ModeSetting mode = new ModeSetting("Mode", "Legit", "Legit", "Rage");
    private final List<Packet<?>> packetsList = new CopyOnWriteArrayList<>();
    private final TimerUtil timer = new TimerUtil();
    private double x, y, z;

    @Override
    public void onMotionEvent(MotionEvent event) {
        this.setSuffix(mode.getMode());
        switch (mode.getMode()) {
            case "Legit":
                if (timer.hasTimeElapsed(50)) {
                    if (!packetsList.isEmpty()) {
                        for (Packet<?> packet : packetsList) {
                            PacketUtils.sendPacketNoEvent(packet);
                            packetsList.remove(packet);
                        }
                    }
                    timer.reset();
                }
                break;
            case "Rage":
                x = mc.thePlayer.posX;
                y = mc.thePlayer.posY;
                z = mc.thePlayer.posZ;
                double yaw = Math.toRadians(mc.thePlayer.rotationYaw);
                double xValue = -Math.sin(yaw) * 2;
                double zValue = Math.cos(yaw) * 2;
                if (KillAura.attacking) {
                    mc.thePlayer.setPosition(x + xValue, y, z + zValue);
                    if (mc.thePlayer.ticksExisted % 2 == 0) {
                        mc.thePlayer.setPosition(x - xValue, y, z - zValue);
                    }
                }
                break;
        }
    }


    @Override
    public void onPacketSendEvent(PacketSendEvent e) {
        if (e.getPacket() instanceof C03PacketPlayer) {
            packetsList.add(e.getPacket());
            e.cancel();
        }
    }

    public AntiAura() {
        super("AntiAura", Category.EXPLOIT, "can cause your enemies aura to break");
        this.addSettings(mode);
    }

}
