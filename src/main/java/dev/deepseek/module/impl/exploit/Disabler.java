package dev.deepseek.module.impl.exploit;

import dev.deepseek.Tenacity;
import dev.deepseek.event.impl.game.WorldEvent;
import dev.deepseek.event.impl.network.PacketReceiveEvent;
import dev.deepseek.event.impl.network.PacketSendEvent;
import dev.deepseek.event.impl.player.MotionEvent;
import dev.deepseek.module.Category;
import dev.deepseek.module.Module;
import dev.deepseek.module.impl.movement.Speed;
import dev.deepseek.module.settings.impl.BooleanSetting;
import dev.deepseek.module.settings.impl.MultipleBoolSetting;
import dev.deepseek.module.settings.impl.NumberSetting;
import dev.deepseek.ui.mainmenu.CustomMainMenu;
import dev.deepseek.utils.misc.MathUtils;
import dev.deepseek.utils.player.ChatUtil;
import dev.deepseek.utils.player.MovementUtils;
import dev.deepseek.utils.player.RotationUtils;
import dev.deepseek.utils.server.PacketUtils;
import dev.deepseek.utils.server.ServerUtils;
import dev.deepseek.utils.time.TimerUtil;
import net.minecraft.client.entity.EntityPlayerSP;
import net.minecraft.client.gui.inventory.GuiInventory;
import net.minecraft.client.multiplayer.GuiConnecting;
import net.minecraft.entity.player.PlayerCapabilities;
import net.minecraft.network.Packet;
import net.minecraft.network.play.client.*;
import net.minecraft.network.play.server.S07PacketRespawn;
import net.minecraft.network.play.server.S08PacketPlayerPosLook;
import net.minecraft.potion.Potion;
import net.minecraft.util.AxisAlignedBB;
import net.minecraft.util.ChatComponentText;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

public final class Disabler extends Module {

    private final MultipleBoolSetting disablers = new MultipleBoolSetting("Disablers",
            new BooleanSetting("Watchdog Strafe", false),
            new BooleanSetting("Watchdog Timer", false),
            new BooleanSetting("MMC", false),
            new BooleanSetting("C06->C04", false),
            new BooleanSetting("C04->C06", false),
            new BooleanSetting("Hover", false),
            new BooleanSetting("Spoof Ground", false),
            new BooleanSetting("C0B Cancel", false),
            new BooleanSetting("C0C Spam", false),
            new BooleanSetting("Verus", false),
            new BooleanSetting("Omni Sprint", false),
            new BooleanSetting("Void TP", false),
            new BooleanSetting("Silent S08", false));

    private final NumberSetting hoverHeight = new NumberSetting("Hover Height", 0.1, 5, 0.01, 0.01);

    private final BooleanSetting onGround = new BooleanSetting("On Ground", true);

    private final NumberSetting TPDelay = new NumberSetting("TP Delay", 20, 100, 5, 1);

    private final TimerUtil timer = new TimerUtil(), joinTimer = new TimerUtil();

    private boolean synced;

    private final CopyOnWriteArrayList<Packet> watchdogPlayerPackets = new CopyOnWriteArrayList<>(), watchdogC0FC00Packets = new CopyOnWriteArrayList<>(), watchdogInvPackets = new CopyOnWriteArrayList<>();

    private final CopyOnWriteArrayList<Packet> packets = new CopyOnWriteArrayList<>();

    private double s08Y;

    public static boolean spiking;
    public static final TimerUtil spikeTimer = new TimerUtil();

    private int airTicks;

    public Disabler() {
        super("Disabler", Category.EXPLOIT, "Disables some anticheats");
        TPDelay.addParent(disablers, voidTPDisabler -> voidTPDisabler.getSetting("Void TP").isEnabled());
        hoverHeight.addParent(disablers, hoverDisabler -> hoverDisabler.getSetting("Hover").isEnabled());
        onGround.addParent(disablers, spoofGroundDisabler -> spoofGroundDisabler.getSetting("Spoof Ground").isEnabled());
        this.addSettings(disablers, TPDelay, onGround, hoverHeight);
    }

    @Override
    public void onMotionEvent(MotionEvent event) {
        final List<BooleanSetting> enabledBooleanSettings = disablers.getBoolSettings().stream().filter(BooleanSetting::isEnabled).collect(Collectors.toList());


        if (enabledBooleanSettings.size() == 1) {
            setSuffix(enabledBooleanSettings.get(0).name);
        } else if (enabledBooleanSettings.size() > 1) {
            setSuffix(enabledBooleanSettings.size() + " Enabled");
        } else {
            setSuffix("None");
        }

        for (BooleanSetting booleanSetting : disablers.getBoolSettings()) {
            if (booleanSetting.isEnabled()) {
                switch (booleanSetting.name) {
                    case "MMC":
                        if (timer.hasTimeElapsed(MathUtils.getRandomInRange(1000, 1500), true)) {
                            packets.forEach(PacketUtils::sendPacketNoEvent);
                            packets.clear();
                        }
                        break;
                    case "Watchdog Timer":
                        if (spikeTimer.hasTimeElapsed(700, true)) {
                            timer.reset();
                        }
                        break;
                    case "C0C Spam":
                        PacketUtils.sendPacketNoEvent(new C0CPacketInput());
                        break;
                }
            }
        }
    }

    @Override
    public void onPacketSendEvent(PacketSendEvent event) {
        if (mc.thePlayer == null) return;
        for (BooleanSetting booleanSetting : disablers.getBoolSettings()) {
            if (booleanSetting.isEnabled()) {
                switch (booleanSetting.name) {
                    case "Watchdog Strafe":
                        if (ServerUtils.isOnHypixel()) {
                            if (event.getPacket() instanceof C03PacketPlayer && !mc.thePlayer.isPotionActive(Potion.jump)) {
                                final C03PacketPlayer c03 = (C03PacketPlayer) event.getPacket();
                                if (DeepSeek.INSTANCE.getModuleCollection().getModule(Speed.class).isEnabled() && mc.thePlayer.fallDistance < 1)
                                    c03.setOnGround(true);

                                if (DeepSeek.INSTANCE.getModuleCollection().getModule(Speed.class).isEnabled() && mc.thePlayer.ticksExisted % 4 != 0 && mc.thePlayer.fallDistance < 1) {
                                    event.cancel();
                                    watchdogPlayerPackets.add(event.getPacket());
                                } else if (!watchdogPlayerPackets.isEmpty()) {
                                    watchdogPlayerPackets.forEach(PacketUtils::sendPacketNoEvent);
                                    watchdogPlayerPackets.clear();
                                }
                            }
                        }
                        break;
                    case "Watchdog Timer":
                        if (ServerUtils.isOnHypixel()) {
                            if (event.getPacket() instanceof C03PacketPlayer) {
                                final C03PacketPlayer c03 = (C03PacketPlayer) event.getPacket();
                                if (!c03.isMoving() && !c03.getRotating()) {
                                    event.cancel();
                                    break;
                                }
                            }
                            if (!timer.hasTimeElapsed(350)) {
                                if (event.getPacket() instanceof C0FPacketConfirmTransaction || event.getPacket() instanceof C00PacketKeepAlive) {
                                    event.cancel();
                                    watchdogC0FC00Packets.add(event.getPacket());
                                }
                            } else if (!watchdogC0FC00Packets.isEmpty()) {
                                watchdogC0FC00Packets.forEach(PacketUtils::sendPacketNoEvent);
                                watchdogC0FC00Packets.clear();
                            }
                        }
                        break;
                    case "Void TP":
                        if (event.getPacket() instanceof C03PacketPlayer) {
                            final C03PacketPlayer c03 = (C03PacketPlayer) event.getPacket();
                            if (mc.thePlayer.ticksExisted % TPDelay.getValue() == 0) {
                                c03.setY(c03.getPositionY() - 1000);
                            }
                        }
                        break;
                    case "MMC":
                        if (event.getPacket() instanceof C0BPacketEntityAction) {
                            final C0BPacketEntityAction c0b = (C0BPacketEntityAction) event.getPacket();
                            if (c0b.getAction().equals(C0BPacketEntityAction.Action.START_SPRINTING)) {
                                if (EntityPlayerSP.serverSprintState) {
                                    PacketUtils.sendPacketNoEvent(new C0BPacketEntityAction(mc.thePlayer, C0BPacketEntityAction.Action.STOP_SPRINTING));
                                    EntityPlayerSP.serverSprintState = false;
                                }
                            }
                            event.cancel();
                        }

                        if (event.getPacket() instanceof C0FPacketConfirmTransaction || event.getPacket() instanceof C00PacketKeepAlive) {
                            event.cancel();
                            packets.add(event.getPacket());
                        }
                        break;
                    case "C06->C04":
                        if (event.getPacket() instanceof C03PacketPlayer.C06PacketPlayerPosLook) {
                            final C03PacketPlayer.C06PacketPlayerPosLook c06 = (C03PacketPlayer.C06PacketPlayerPosLook) event.getPacket();
                            event.setPacket(new C03PacketPlayer.C04PacketPlayerPosition(c06.getPositionX(), c06.getPositionY(), c06.getPositionZ(), c06.isOnGround()));
                        }
                        break;
                    case "C04->C06":
                        if (event.getPacket() instanceof C03PacketPlayer.C04PacketPlayerPosition) {
                            final C03PacketPlayer.C04PacketPlayerPosition c04 = (C03PacketPlayer.C04PacketPlayerPosition) event.getPacket();
                            event.setPacket(new C03PacketPlayer.C06PacketPlayerPosLook(c04.getPositionX(), c04.getPositionY(), c04.getPositionZ(), mc.thePlayer.rotationYaw, mc.thePlayer.rotationPitch, c04.isOnGround()));
                        }
                        break;
                    case "Hover":
                        if (event.getPacket() instanceof C03PacketPlayer) {
                            final C03PacketPlayer c03 = (C03PacketPlayer) event.getPacket();
                            c03.setY(mc.thePlayer.posY + hoverHeight.getValue());
                        }
                        break;
                    case "Spoof Ground":
                        if (event.getPacket() instanceof C03PacketPlayer) {
                            final C03PacketPlayer c03 = (C03PacketPlayer) event.getPacket();
                            c03.setOnGround(onGround.isEnabled());
                        }
                        break;
                    case "C0B Cancel":
                        if (event.getPacket() instanceof C0BPacketEntityAction) event.cancel();
                        break;
                    case "Verus":
                        if (event.getPacket() instanceof C0FPacketConfirmTransaction || event.getPacket() instanceof C00PacketKeepAlive)
                            event.cancel();
                        break;
                    case "Omni Sprint":
                        if (event.getPacket() instanceof C0BPacketEntityAction) {
                            final C0BPacketEntityAction c0b = (C0BPacketEntityAction) event.getPacket();
                            if (c0b.getAction().equals(C0BPacketEntityAction.Action.START_SPRINTING)) {
                                if (EntityPlayerSP.serverSprintState) {
                                    PacketUtils.sendPacketNoEvent(new C0BPacketEntityAction(mc.thePlayer, C0BPacketEntityAction.Action.STOP_SPRINTING));
                                    EntityPlayerSP.serverSprintState = false;
                                }
                            }
                            event.cancel();
                        }
                        break;
                }
            }
        }
    }

    @Override
    public void onPacketReceiveEvent(PacketReceiveEvent event) {
        for (BooleanSetting booleanSetting : disablers.getBoolSettings()) {
            if (booleanSetting.isEnabled()) {
                switch (booleanSetting.name) {
                    case "Silent S08":
                        if (event.getPacket() instanceof S08PacketPlayerPosLook) {
                            final S08PacketPlayerPosLook s08 = (S08PacketPlayerPosLook) event.getPacket();
                            event.cancel();
                            PacketUtils.sendPacketNoEvent(new C03PacketPlayer.C06PacketPlayerPosLook(s08.getX(), s08.getY(), s08.getZ(), s08.getYaw(), s08.getPitch(), false));
                        }
                        break;
                }
            }
        }
    }

    private boolean isBlockUnder() {
        if (mc.thePlayer.posY < 0) return false;
        for (int offset = 0; offset < (int) mc.thePlayer.posY + 2; offset += 2) {
            AxisAlignedBB bb = mc.thePlayer.getEntityBoundingBox().offset(0, -offset, 0);
            if (!mc.theWorld.getCollidingBoundingBoxes(mc.thePlayer, bb).isEmpty()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void onWorldEvent(WorldEvent event) {
        watchdogC0FC00Packets.clear();
        timer.reset();
    }
}

