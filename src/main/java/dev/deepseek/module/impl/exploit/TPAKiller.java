package dev.deepseek.module.impl.exploit;

import dev.deepseek.event.impl.network.PacketReceiveEvent;
import dev.deepseek.module.Category;
import dev.deepseek.module.Module;
import net.minecraft.network.play.server.S02PacketChat;

public final class T<PERSON><PERSON>iller extends Module {

    @Override
    public void onPacketReceiveEvent(PacketReceiveEvent event) {
        if (event.getPacket() instanceof S02PacketChat && mc.thePlayer != null) {
            S02PacketChat s02PacketChat = (S02PacketChat) event.getPacket();
            String msg = s02PacketChat.getChatComponent().getUnformattedText();
            if (msg.contains("tpa") || msg.contains("request")) {
                mc.thePlayer.setPosition(mc.thePlayer.posX, mc.thePlayer.posY - 6, mc.thePlayer.posZ);
                mc.thePlayer.sendChatMessage("/tpa accept");
                if (mc.thePlayer.ticksExisted % 2 == 0) {
                    mc.thePlayer.setPosition(mc.thePlayer.posX, mc.thePlayer.posY + 6, mc.thePlayer.posZ);
                }
            }
        }
    }

    public TPAKiller() {
        super("TPAKiller", Category.EXPLOIT, "Suffocates people who /tpa to you");
    }

}
