package dev.deepseek.module.impl.player;

import dev.deepseek.event.impl.player.SafeWalkEvent;
import dev.deepseek.module.Category;
import dev.deepseek.module.Module;

public final class SafeWalk extends Module {
    @Override
    public void onSafeWalkEvent(SafeWalkEvent e) {
        if (mc.thePlayer == null) return;
        e.setSafe(true);
    }

    public SafeWalk() {
        super("SafeWalk", Category.PLAYER, "prevents walking off blocks");
    }

}
