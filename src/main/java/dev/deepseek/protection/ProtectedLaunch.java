package dev.deepseek.protection;

import dev.deepseek.DeepSeek;
import dev.deepseek.commands.CommandHandler;
import dev.deepseek.commands.impl.*;
import dev.deepseek.config.ConfigManager;
import dev.deepseek.config.DragManager;
import dev.deepseek.intent.api.account.IntentAccount;
import dev.deepseek.module.BackgroundProcess;
import dev.deepseek.module.Module;
import dev.deepseek.module.ModuleCollection;
import dev.deepseek.module.api.TargetManager;
import dev.deepseek.module.impl.combat.*;
import dev.deepseek.module.impl.exploit.*;
import dev.deepseek.module.impl.misc.*;
import dev.deepseek.module.impl.movement.*;
import dev.deepseek.module.impl.player.*;
import dev.deepseek.module.impl.render.*;
import dev.deepseek.module.impl.render.killeffects.KillEffects;
import dev.deepseek.module.impl.render.wings.DragonWings;
import dev.deepseek.scripting.api.ScriptManager;
import dev.deepseek.ui.altmanager.GuiAltManager;
import dev.deepseek.ui.altmanager.helpers.KingGenApi;
import dev.deepseek.utils.render.EntityCulling;
import dev.deepseek.utils.render.Theme;
import dev.deepseek.utils.server.PingerUtils;
import dev.deepseek.viamcp.ViaMCP;
import net.minecraft.client.Minecraft;
import store.intent.intentguard.annotation.Bootstrap;
import store.intent.intentguard.annotation.Native;

import java.io.File;
import java.util.Arrays;
import java.util.HashMap;

@Native
public class ProtectedLaunch {

    private static final HashMap<Object, Module> modules = new HashMap<>();

    @Native
    @Bootstrap
    public static void start() {
        // Setup Intent API access
        DeepSeek.INSTANCE.setIntentAccount(new IntentAccount());
        DeepSeek.INSTANCE.setModuleCollection(new ModuleCollection());

        // Combat
        modules.put(KillAura.class, new KillAura());
        modules.put(Velocity.class, new Velocity());
        modules.put(Criticals.class, new Criticals());
        modules.put(AutoHead.class, new AutoHead());
        modules.put(AutoPot.class, new AutoPot());
        modules.put(FastBow.class, new FastBow());
        modules.put(KeepSprint.class, new KeepSprint());
        modules.put(IdleFighter.class, new IdleFighter());
        modules.put(SuperKnockback.class, new SuperKnockback());
        modules.put(TargetManager.class, new TargetManager());

        // Exploit
        modules.put(Disabler.class, new Disabler());
        modules.put(AntiInvis.class, new AntiInvis());
        modules.put(Regen.class, new Regen());
        modules.put(TPAKiller.class, new TPAKiller());
        modules.put(AntiAura.class, new AntiAura());
        modules.put(AntiAim.class, new AntiAim());
        modules.put(ResetVL.class, new ResetVL());
        modules.put(Crasher.class, new Crasher());

        // Misc
        modules.put(AntiDesync.class, new AntiDesync());
        modules.put(AntiTabComplete.class, new AntiTabComplete());
        modules.put(Spammer.class, new Spammer());
        modules.put(AntiFreeze.class, new AntiFreeze());
        modules.put(LightningTracker.class, new LightningTracker());
        modules.put(MurderDetector.class, new MurderDetector());
        modules.put(AutoHypixel.class, new AutoHypixel());
        modules.put(NoRotate.class, new NoRotate());
        modules.put(AutoRespawn.class, new AutoRespawn());
        modules.put(MCF.class, new MCF());
        modules.put(AutoAuthenticate.class, new AutoAuthenticate());
        modules.put(Killsults.class, new Killsults());
        modules.put(Sniper.class, new Sniper());

        // Movement
        modules.put(Sprint.class, new Sprint());
        modules.put(Scaffold.class, new Scaffold());
        modules.put(Speed.class, new Speed());
        modules.put(Flight.class, new Flight());
        modules.put(LongJump.class, new LongJump());
        modules.put(Step.class, new Step());
        modules.put(TargetStrafe.class, new TargetStrafe());
        modules.put(FastLadder.class, new FastLadder());
        modules.put(InventoryMove.class, new InventoryMove());
        modules.put(Jesus.class, new Jesus());
        modules.put(Spider.class, new Spider());
        modules.put(AutoHeadHitter.class, new AutoHeadHitter());

        // Player
        modules.put(ChestStealer.class, new ChestStealer());
        modules.put(InvManager.class, new InvManager());
        modules.put(AutoArmor.class, new AutoArmor());
        modules.put(SpeedMine.class, new SpeedMine());
        modules.put(Blink.class, new Blink());
        modules.put(NoFall.class, new NoFall());
        modules.put(Timer.class, new Timer());
        modules.put(Freecam.class, new Freecam());
        modules.put(FastPlace.class, new FastPlace());
        modules.put(SafeWalk.class, new SafeWalk());
        modules.put(NoSlow.class, new NoSlow());
        modules.put(AutoTool.class, new AutoTool());
        modules.put(AntiVoid.class, new AntiVoid());
        modules.put(KillEffects.class, new KillEffects());

        // Render
        modules.put(ArrayListMod.class, new ArrayListMod());
        modules.put(NotificationsMod.class, new NotificationsMod());
        modules.put(ScoreboardMod.class, new ScoreboardMod());
        modules.put(HUDMod.class, new HUDMod());
        modules.put(ClickGUIMod.class, new ClickGUIMod());
        modules.put(Radar.class, new Radar());
        modules.put(Animations.class, new Animations());
        modules.put(SpotifyMod.class, new SpotifyMod());
        modules.put(Ambience.class, new Ambience());
        modules.put(ChinaHat.class, new ChinaHat());
        modules.put(GlowESP.class, new GlowESP());
        modules.put(Brightness.class, new Brightness());
        modules.put(ESP2D.class, new ESP2D());
        modules.put(PostProcessing.class, new PostProcessing());
        modules.put(Statistics.class, new Statistics());
        modules.put(TargetHUDMod.class, new TargetHUDMod());
        modules.put(Glint.class, new Glint());
        modules.put(Breadcrumbs.class, new Breadcrumbs());
        modules.put(Streamer.class, new Streamer());
        modules.put(Hitmarkers.class, new Hitmarkers());
        modules.put(NoHurtCam.class, new NoHurtCam());
        modules.put(Keystrokes.class, new Keystrokes());
        modules.put(ItemPhysics.class, new ItemPhysics());
        modules.put(XRay.class, new XRay());
        modules.put(EntityCulling.class, new EntityCulling());
        modules.put(DragonWings.class, new DragonWings());
        modules.put(PlayerList.class, new PlayerList());
        modules.put(JumpCircle.class, new JumpCircle());
        modules.put(CustomModel.class, new CustomModel());
        modules.put(EntityEffects.class, new EntityEffects());
        modules.put(Chams.class, new Chams());
        modules.put(BrightPlayers.class, new BrightPlayers());

        DeepSeek.INSTANCE.getModuleCollection().setModules(modules);

        Theme.init();

        DeepSeek.INSTANCE.setPingerUtils(new PingerUtils());

        DeepSeek.INSTANCE.setScriptManager(new ScriptManager());

        CommandHandler commandHandler = new CommandHandler();
        commandHandler.commands.addAll(Arrays.asList(
                new FriendCommand(), new CopyNameCommand(), new BindCommand(), new UnbindCommand(),
                new ScriptCommand(), new SettingCommand(), new HelpCommand(),
                new VClipCommand(), new ClearBindsCommand(), new ClearConfigCommand(),
                new LoadCommand(), new ToggleCommand()
        ));
        DeepSeek.INSTANCE.setCommandHandler(commandHandler);
        DeepSeek.INSTANCE.getEventProtocol().register(new BackgroundProcess());

        DeepSeek.INSTANCE.setConfigManager(new ConfigManager());
        ConfigManager.defaultConfig = new File(Minecraft.getMinecraft().mcDataDir + "/DeepSeek/Config.json");
        DeepSeek.INSTANCE.getConfigManager().collectConfigs();
        if (ConfigManager.defaultConfig.exists()) {
            DeepSeek.INSTANCE.getConfigManager().loadConfig(DeepSeek.INSTANCE.getConfigManager().readConfigData(ConfigManager.defaultConfig.toPath()), true);
        }

        DragManager.loadDragData();

        DeepSeek.INSTANCE.setAltManager(new GuiAltManager());

        DeepSeek.INSTANCE.setKingGenApi(new KingGenApi());

        try {
            DeepSeek.LOGGER.info("Starting ViaMCP...");
            ViaMCP viaMCP = ViaMCP.getInstance();
            viaMCP.start();
            viaMCP.initAsyncSlider(100, 100, 110, 20);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @SafeVarargs
    private static void addModules(Class<? extends Module>... classes) {
        for (Class<? extends Module> moduleClass : classes) {
            try {
                modules.put(moduleClass, moduleClass.newInstance());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

}
