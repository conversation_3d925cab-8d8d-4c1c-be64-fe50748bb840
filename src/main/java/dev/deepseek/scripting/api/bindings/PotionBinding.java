package dev.deepseek.scripting.api.bindings;

import store.intent.intentguard.annotation.Exclude;
import store.intent.intentguard.annotation.Strategy;

@Exclude(Strategy.NAME_REMAPPING)
public class PotionBinding {
    public final int moveSpeed = 1;
    public final int moveSlowdown = 2;
    public final int digSpeed = 3;
    public final int digSlowdown = 4;
    public final int damageBoost = 5;
    public final int heal = 6;
    public final int harm = 7;
    public final int jump = 8;
    public final int confusion = 9;
    public final int regeneration = 10;
    public final int resistance = 11;
    public final int fireResistance = 12;
    public final int waterBreathing = 13;
    public final int invisibility = 14;
    public final int blindness = 15;
    public final int nightVision = 16;
    public final int hunger = 17;
    public final int weakness = 18;
    public final int poison = 19;
    public final int wither = 20;
    public final int healthBoost = 21;
    public final int absorption = 22;
    public final int saturation = 23;
}
