package dev.deepseek.scripting.api.bindings;

import dev.deepseek.Tenacity;
import store.intent.intentguard.annotation.Exclude;
import store.intent.intentguard.annotation.Strategy;

@Exclude(Strategy.NAME_REMAPPING)
public class UserBinding {

    public String uid() {
        return String.valueOf(DeepSeek.INSTANCE.getIntentAccount().client_uid);
    }

    public String username() {
        return String.valueOf(DeepSeek.INSTANCE.getIntentAccount().username);
    }

    public String discordTag() {
        return String.valueOf(DeepSeek.INSTANCE.getIntentAccount().discord_tag);
    }

}
