package dev.tenacity.viamcp.protocols;

public class ProtocolInfoCollection {
    private static final String NO_DESC = "No Description Available";

    public static ProtocolInfo R1_19_1 = new ProtocolInfo(
            "The Wild Update", NO_DESC, "August 5, 2022"
    );

    public static ProtocolInfo R1_19 = new ProtocolInfo(
            "The Wild Update", NO_DESC, "June 7, 2022"
    );

    public static ProtocolInfo R1_18_2 = new ProtocolInfo(
            "Caves & Cliffs: Part II", NO_DESC, "February 28, 2022"
    );

    public static ProtocolInfo R1_18 = new ProtocolInfo(
            "Caves & Cliffs: Part II", NO_DESC, "November 30, 2021 - December 10, 2021"
    );

    public static ProtocolInfo R1_17_1 = new ProtocolInfo(
            "Caves & Cliffs: Part I", NO_DESC, "July 6, 2021"
    );

    public static ProtocolInfo R1_17 = new ProtocolInfo(
            "Caves & Cliffs: Part I", NO_DESC, "June 8, 2021"
    );

    public static ProtocolInfo R1_16_4 = new ProtocolInfo(
            "Nether Update", NO_DESC, "November 2, 2020 - January 13, 2021"
    );

    public static ProtocolInfo R1_16_3 = new ProtocolInfo(
            "Nether Update", NO_DESC, "September 7, 2020"
    );

    public static ProtocolInfo R1_16_2 = new ProtocolInfo(
            "Nether Update", NO_DESC, "August 11, 2020"
    );

    public static ProtocolInfo R1_16_1 = new ProtocolInfo(
            "Nether Update", NO_DESC, "June 24, 2020"
    );

    public static ProtocolInfo R1_16 = new ProtocolInfo(
            "Nether Update", NO_DESC, "June 23, 2020"
    );

    public static ProtocolInfo R1_15_2 = new ProtocolInfo(
            "Buzzy Bees", NO_DESC, "January 21, 2020"
    );

    public static ProtocolInfo R1_15_1 = new ProtocolInfo(
            "Buzzy Bees", NO_DESC, "December 17, 2019"
    );

    public static ProtocolInfo R1_15 = new ProtocolInfo(
            "Buzzy Bees", NO_DESC, "December 10, 2019"
    );

    public static ProtocolInfo R1_14_4 = new ProtocolInfo(
            "Village & Pillage", NO_DESC, "July 19, 2019"
    );

    public static ProtocolInfo R1_14_3 = new ProtocolInfo(
            "Village & Pillage", NO_DESC, "June 24, 2019"
    );

    public static ProtocolInfo R1_14_2 = new ProtocolInfo(
            "Village & Pillage", NO_DESC, "May 27, 2019"
    );

    public static ProtocolInfo R1_14_1 = new ProtocolInfo(
            "Village & Pillage", NO_DESC, "May 13, 2019"
    );

    public static ProtocolInfo R1_14 = new ProtocolInfo(
            "Village & Pillage", NO_DESC, "April 23, 2019"
    );

    public static ProtocolInfo R1_13_2 = new ProtocolInfo(
            "Update Aquatic", NO_DESC, "October 22, 2018"
    );

    public static ProtocolInfo R1_13_1 = new ProtocolInfo(
            "Update Aquatic", NO_DESC, "August 22, 2018"
    );

    public static ProtocolInfo R1_13 = new ProtocolInfo(
            "Update Aquatic", NO_DESC, "July 18, 2018"
    );

    public static ProtocolInfo R1_12_2 = new ProtocolInfo(
            "World of Color Update", NO_DESC, "September 18, 2017"
    );

    public static ProtocolInfo R1_12_1 = new ProtocolInfo(
            "World of Color Update", NO_DESC, "August 3, 2017"
    );

    public static ProtocolInfo R1_12 = new ProtocolInfo(
            "World of Color Update", NO_DESC, "June 7, 2017"
    );

    public static ProtocolInfo R1_11_1 = new ProtocolInfo(
            "Exploration Update", NO_DESC, "December 20, 2016 - December 21, 2016"
    );

    public static ProtocolInfo R1_11 = new ProtocolInfo(
            "Exploration Update", NO_DESC, "November 14, 2016"
    );

    public static ProtocolInfo R1_10 = new ProtocolInfo(
            "Frostburn Update", NO_DESC, "June 8, 2016 - June 23, 2016"
    );

    public static ProtocolInfo R1_9_3 = new ProtocolInfo(
            "Combat Update", NO_DESC, "May 10, 2016"
    );

    public static ProtocolInfo R1_9_2 = new ProtocolInfo(
            "Combat Update", NO_DESC, "March 30, 2016"
    );

    public static ProtocolInfo R1_9_1 = new ProtocolInfo(
            "Combat Update", NO_DESC, "March 30, 2016"
    );

    public static ProtocolInfo R1_9 = new ProtocolInfo(
            "Combat Update", NO_DESC, "February 29, 2016"
    );

    public static ProtocolInfo R1_8 = new ProtocolInfo(
            "Bountiful Update", NO_DESC, "September 2, 2014 - December 9, 2015"
    );

    public static ProtocolInfo R1_7_6 = new ProtocolInfo(
            "The Update that Changed the World", NO_DESC, "April 9, 2014 - June 26, 2014"
    );

    public static ProtocolInfo R1_7 = new ProtocolInfo(
            "The Update that Changed the World", NO_DESC, "October 22, 2013 - February 26, 2014"
    );
}
