package dev.tenacity.module.impl.exploit;

import dev.tenacity.event.impl.player.MotionEvent;
import dev.tenacity.module.Category;
import dev.tenacity.module.Module;
import net.minecraft.potion.Potion;

@SuppressWarnings("unused")
public final class AntiInvis extends Module {

    public AntiInvis() {
        super("AntiInvis", Category.EXPLOIT, "Shows invisible people");
    }

    @Override
    public void onMotionEvent(MotionEvent event) {
        mc.theWorld.playerEntities.stream()
                .filter(player -> player != mc.thePlayer && player.isPotionActive(Potion.invisibility))
                .forEach(player -> {
                    player.removePotionEffect(Potion.invisibility.getId());
                    player.setInvisible(false);
                });
    }

}
