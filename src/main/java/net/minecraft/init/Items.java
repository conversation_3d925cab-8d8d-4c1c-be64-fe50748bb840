package net.minecraft.init;

import net.minecraft.item.*;
import net.minecraft.util.ResourceLocation;

public class Items {
    public static final Item iron_shovel;
    public static final Item iron_pickaxe;
    public static final Item iron_axe;
    public static final Item flint_and_steel;
    public static final Item apple;
    public static final ItemBow bow;
    public static final Item arrow;
    public static final Item coal;
    public static final Item diamond;
    public static final Item iron_ingot;
    public static final Item gold_ingot;
    public static final Item iron_sword;
    public static final Item wooden_sword;
    public static final Item wooden_shovel;
    public static final Item wooden_pickaxe;
    public static final Item wooden_axe;
    public static final Item stone_sword;
    public static final Item stone_shovel;
    public static final Item stone_pickaxe;
    public static final Item stone_axe;
    public static final Item diamond_sword;
    public static final Item diamond_shovel;
    public static final Item diamond_pickaxe;
    public static final Item diamond_axe;
    public static final Item stick;
    public static final Item bowl;
    public static final Item mushroom_stew;
    public static final Item golden_sword;
    public static final Item golden_shovel;
    public static final Item golden_pickaxe;
    public static final Item golden_axe;
    public static final Item string;
    public static final Item feather;
    public static final Item gunpowder;
    public static final Item wooden_hoe;
    public static final Item stone_hoe;
    public static final Item iron_hoe;
    public static final Item diamond_hoe;
    public static final Item golden_hoe;
    public static final Item wheat_seeds;
    public static final Item wheat;
    public static final Item bread;
    public static final ItemArmor leather_helmet;
    public static final ItemArmor leather_chestplate;
    public static final ItemArmor leather_leggings;
    public static final ItemArmor leather_boots;
    public static final ItemArmor chainmail_helmet;
    public static final ItemArmor chainmail_chestplate;
    public static final ItemArmor chainmail_leggings;
    public static final ItemArmor chainmail_boots;
    public static final ItemArmor iron_helmet;
    public static final ItemArmor iron_chestplate;
    public static final ItemArmor iron_leggings;
    public static final ItemArmor iron_boots;
    public static final ItemArmor diamond_helmet;
    public static final ItemArmor diamond_chestplate;
    public static final ItemArmor diamond_leggings;
    public static final ItemArmor diamond_boots;
    public static final ItemArmor golden_helmet;
    public static final ItemArmor golden_chestplate;
    public static final ItemArmor golden_leggings;
    public static final ItemArmor golden_boots;
    public static final Item flint;
    public static final Item porkchop;
    public static final Item cooked_porkchop;
    public static final Item painting;
    public static final Item golden_apple;
    public static final Item sign;
    public static final Item oak_door;
    public static final Item spruce_door;
    public static final Item birch_door;
    public static final Item jungle_door;
    public static final Item acacia_door;
    public static final Item dark_oak_door;
    public static final Item bucket;
    public static final Item water_bucket;
    public static final Item lava_bucket;
    public static final Item minecart;
    public static final Item saddle;
    public static final Item iron_door;
    public static final Item redstone;
    public static final Item snowball;
    public static final Item boat;
    public static final Item leather;
    public static final Item milk_bucket;
    public static final Item brick;
    public static final Item clay_ball;
    public static final Item reeds;
    public static final Item paper;
    public static final Item book;
    public static final Item slime_ball;
    public static final Item chest_minecart;
    public static final Item furnace_minecart;
    public static final Item egg;
    public static final Item compass;
    public static final ItemFishingRod fishing_rod;
    public static final Item clock;
    public static final Item glowstone_dust;
    public static final Item fish;
    public static final Item cooked_fish;
    public static final Item dye;
    public static final Item bone;
    public static final Item sugar;
    public static final Item cake;
    public static final Item bed;
    public static final Item repeater;
    public static final Item cookie;
    public static final ItemMap filled_map;
    public static final ItemShears shears;
    public static final Item melon;
    public static final Item pumpkin_seeds;
    public static final Item melon_seeds;
    public static final Item beef;
    public static final Item cooked_beef;
    public static final Item chicken;
    public static final Item cooked_chicken;
    public static final Item mutton;
    public static final Item cooked_mutton;
    public static final Item rabbit;
    public static final Item cooked_rabbit;
    public static final Item rabbit_stew;
    public static final Item rabbit_foot;
    public static final Item rabbit_hide;
    public static final Item rotten_flesh;
    public static final Item ender_pearl;
    public static final Item blaze_rod;
    public static final Item ghast_tear;
    public static final Item gold_nugget;
    public static final Item nether_wart;
    public static final ItemPotion potionitem;
    public static final Item glass_bottle;
    public static final Item spider_eye;
    public static final Item fermented_spider_eye;
    public static final Item blaze_powder;
    public static final Item magma_cream;
    public static final Item brewing_stand;
    public static final Item cauldron;
    public static final Item ender_eye;
    public static final Item speckled_melon;
    public static final Item spawn_egg;
    public static final Item experience_bottle;
    public static final Item fire_charge;
    public static final Item writable_book;
    public static final Item written_book;
    public static final Item emerald;
    public static final Item item_frame;
    public static final Item flower_pot;
    public static final Item carrot;
    public static final Item potato;
    public static final Item baked_potato;
    public static final Item poisonous_potato;
    public static final ItemEmptyMap map;
    public static final Item golden_carrot;
    public static final Item skull;
    public static final Item carrot_on_a_stick;
    public static final Item nether_star;
    public static final Item pumpkin_pie;
    public static final Item fireworks;
    public static final Item firework_charge;
    public static final ItemEnchantedBook enchanted_book;
    public static final Item comparator;
    public static final Item netherbrick;
    public static final Item quartz;
    public static final Item tnt_minecart;
    public static final Item hopper_minecart;
    public static final ItemArmorStand armor_stand;
    public static final Item iron_horse_armor;
    public static final Item golden_horse_armor;
    public static final Item diamond_horse_armor;
    public static final Item lead;
    public static final Item name_tag;
    public static final Item command_block_minecart;
    public static final Item record_13;
    public static final Item record_cat;
    public static final Item record_blocks;
    public static final Item record_chirp;
    public static final Item record_far;
    public static final Item record_mall;
    public static final Item record_mellohi;
    public static final Item record_stal;
    public static final Item record_strad;
    public static final Item record_ward;
    public static final Item record_11;
    public static final Item record_wait;
    public static final Item prismarine_shard;
    public static final Item prismarine_crystals;
    public static final Item banner;

    private static Item getRegisteredItem(String name) {
        return (Item) Item.itemRegistry.getObject(new ResourceLocation(name));
    }

    static {
        if (!Bootstrap.isRegistered()) {
            throw new RuntimeException("Accessed Items before Bootstrap!");
        } else {
            iron_shovel = getRegisteredItem("iron_shovel");
            iron_pickaxe = getRegisteredItem("iron_pickaxe");
            iron_axe = getRegisteredItem("iron_axe");
            flint_and_steel = getRegisteredItem("flint_and_steel");
            apple = getRegisteredItem("apple");
            bow = (ItemBow) getRegisteredItem("bow");
            arrow = getRegisteredItem("arrow");
            coal = getRegisteredItem("coal");
            diamond = getRegisteredItem("diamond");
            iron_ingot = getRegisteredItem("iron_ingot");
            gold_ingot = getRegisteredItem("gold_ingot");
            iron_sword = getRegisteredItem("iron_sword");
            wooden_sword = getRegisteredItem("wooden_sword");
            wooden_shovel = getRegisteredItem("wooden_shovel");
            wooden_pickaxe = getRegisteredItem("wooden_pickaxe");
            wooden_axe = getRegisteredItem("wooden_axe");
            stone_sword = getRegisteredItem("stone_sword");
            stone_shovel = getRegisteredItem("stone_shovel");
            stone_pickaxe = getRegisteredItem("stone_pickaxe");
            stone_axe = getRegisteredItem("stone_axe");
            diamond_sword = getRegisteredItem("diamond_sword");
            diamond_shovel = getRegisteredItem("diamond_shovel");
            diamond_pickaxe = getRegisteredItem("diamond_pickaxe");
            diamond_axe = getRegisteredItem("diamond_axe");
            stick = getRegisteredItem("stick");
            bowl = getRegisteredItem("bowl");
            mushroom_stew = getRegisteredItem("mushroom_stew");
            golden_sword = getRegisteredItem("golden_sword");
            golden_shovel = getRegisteredItem("golden_shovel");
            golden_pickaxe = getRegisteredItem("golden_pickaxe");
            golden_axe = getRegisteredItem("golden_axe");
            string = getRegisteredItem("string");
            feather = getRegisteredItem("feather");
            gunpowder = getRegisteredItem("gunpowder");
            wooden_hoe = getRegisteredItem("wooden_hoe");
            stone_hoe = getRegisteredItem("stone_hoe");
            iron_hoe = getRegisteredItem("iron_hoe");
            diamond_hoe = getRegisteredItem("diamond_hoe");
            golden_hoe = getRegisteredItem("golden_hoe");
            wheat_seeds = getRegisteredItem("wheat_seeds");
            wheat = getRegisteredItem("wheat");
            bread = getRegisteredItem("bread");
            leather_helmet = (ItemArmor) getRegisteredItem("leather_helmet");
            leather_chestplate = (ItemArmor) getRegisteredItem("leather_chestplate");
            leather_leggings = (ItemArmor) getRegisteredItem("leather_leggings");
            leather_boots = (ItemArmor) getRegisteredItem("leather_boots");
            chainmail_helmet = (ItemArmor) getRegisteredItem("chainmail_helmet");
            chainmail_chestplate = (ItemArmor) getRegisteredItem("chainmail_chestplate");
            chainmail_leggings = (ItemArmor) getRegisteredItem("chainmail_leggings");
            chainmail_boots = (ItemArmor) getRegisteredItem("chainmail_boots");
            iron_helmet = (ItemArmor) getRegisteredItem("iron_helmet");
            iron_chestplate = (ItemArmor) getRegisteredItem("iron_chestplate");
            iron_leggings = (ItemArmor) getRegisteredItem("iron_leggings");
            iron_boots = (ItemArmor) getRegisteredItem("iron_boots");
            diamond_helmet = (ItemArmor) getRegisteredItem("diamond_helmet");
            diamond_chestplate = (ItemArmor) getRegisteredItem("diamond_chestplate");
            diamond_leggings = (ItemArmor) getRegisteredItem("diamond_leggings");
            diamond_boots = (ItemArmor) getRegisteredItem("diamond_boots");
            golden_helmet = (ItemArmor) getRegisteredItem("golden_helmet");
            golden_chestplate = (ItemArmor) getRegisteredItem("golden_chestplate");
            golden_leggings = (ItemArmor) getRegisteredItem("golden_leggings");
            golden_boots = (ItemArmor) getRegisteredItem("golden_boots");
            flint = getRegisteredItem("flint");
            porkchop = getRegisteredItem("porkchop");
            cooked_porkchop = getRegisteredItem("cooked_porkchop");
            painting = getRegisteredItem("painting");
            golden_apple = getRegisteredItem("golden_apple");
            sign = getRegisteredItem("sign");
            oak_door = getRegisteredItem("wooden_door");
            spruce_door = getRegisteredItem("spruce_door");
            birch_door = getRegisteredItem("birch_door");
            jungle_door = getRegisteredItem("jungle_door");
            acacia_door = getRegisteredItem("acacia_door");
            dark_oak_door = getRegisteredItem("dark_oak_door");
            bucket = getRegisteredItem("bucket");
            water_bucket = getRegisteredItem("water_bucket");
            lava_bucket = getRegisteredItem("lava_bucket");
            minecart = getRegisteredItem("minecart");
            saddle = getRegisteredItem("saddle");
            iron_door = getRegisteredItem("iron_door");
            redstone = getRegisteredItem("redstone");
            snowball = getRegisteredItem("snowball");
            boat = getRegisteredItem("boat");
            leather = getRegisteredItem("leather");
            milk_bucket = getRegisteredItem("milk_bucket");
            brick = getRegisteredItem("brick");
            clay_ball = getRegisteredItem("clay_ball");
            reeds = getRegisteredItem("reeds");
            paper = getRegisteredItem("paper");
            book = getRegisteredItem("book");
            slime_ball = getRegisteredItem("slime_ball");
            chest_minecart = getRegisteredItem("chest_minecart");
            furnace_minecart = getRegisteredItem("furnace_minecart");
            egg = getRegisteredItem("egg");
            compass = getRegisteredItem("compass");
            fishing_rod = (ItemFishingRod) getRegisteredItem("fishing_rod");
            clock = getRegisteredItem("clock");
            glowstone_dust = getRegisteredItem("glowstone_dust");
            fish = getRegisteredItem("fish");
            cooked_fish = getRegisteredItem("cooked_fish");
            dye = getRegisteredItem("dye");
            bone = getRegisteredItem("bone");
            sugar = getRegisteredItem("sugar");
            cake = getRegisteredItem("cake");
            bed = getRegisteredItem("bed");
            repeater = getRegisteredItem("repeater");
            cookie = getRegisteredItem("cookie");
            filled_map = (ItemMap) getRegisteredItem("filled_map");
            shears = (ItemShears) getRegisteredItem("shears");
            melon = getRegisteredItem("melon");
            pumpkin_seeds = getRegisteredItem("pumpkin_seeds");
            melon_seeds = getRegisteredItem("melon_seeds");
            beef = getRegisteredItem("beef");
            cooked_beef = getRegisteredItem("cooked_beef");
            chicken = getRegisteredItem("chicken");
            cooked_chicken = getRegisteredItem("cooked_chicken");
            mutton = getRegisteredItem("mutton");
            cooked_mutton = getRegisteredItem("cooked_mutton");
            rabbit = getRegisteredItem("rabbit");
            cooked_rabbit = getRegisteredItem("cooked_rabbit");
            rabbit_stew = getRegisteredItem("rabbit_stew");
            rabbit_foot = getRegisteredItem("rabbit_foot");
            rabbit_hide = getRegisteredItem("rabbit_hide");
            rotten_flesh = getRegisteredItem("rotten_flesh");
            ender_pearl = getRegisteredItem("ender_pearl");
            blaze_rod = getRegisteredItem("blaze_rod");
            ghast_tear = getRegisteredItem("ghast_tear");
            gold_nugget = getRegisteredItem("gold_nugget");
            nether_wart = getRegisteredItem("nether_wart");
            potionitem = (ItemPotion) getRegisteredItem("potion");
            glass_bottle = getRegisteredItem("glass_bottle");
            spider_eye = getRegisteredItem("spider_eye");
            fermented_spider_eye = getRegisteredItem("fermented_spider_eye");
            blaze_powder = getRegisteredItem("blaze_powder");
            magma_cream = getRegisteredItem("magma_cream");
            brewing_stand = getRegisteredItem("brewing_stand");
            cauldron = getRegisteredItem("cauldron");
            ender_eye = getRegisteredItem("ender_eye");
            speckled_melon = getRegisteredItem("speckled_melon");
            spawn_egg = getRegisteredItem("spawn_egg");
            experience_bottle = getRegisteredItem("experience_bottle");
            fire_charge = getRegisteredItem("fire_charge");
            writable_book = getRegisteredItem("writable_book");
            written_book = getRegisteredItem("written_book");
            emerald = getRegisteredItem("emerald");
            item_frame = getRegisteredItem("item_frame");
            flower_pot = getRegisteredItem("flower_pot");
            carrot = getRegisteredItem("carrot");
            potato = getRegisteredItem("potato");
            baked_potato = getRegisteredItem("baked_potato");
            poisonous_potato = getRegisteredItem("poisonous_potato");
            map = (ItemEmptyMap) getRegisteredItem("map");
            golden_carrot = getRegisteredItem("golden_carrot");
            skull = getRegisteredItem("skull");
            carrot_on_a_stick = getRegisteredItem("carrot_on_a_stick");
            nether_star = getRegisteredItem("nether_star");
            pumpkin_pie = getRegisteredItem("pumpkin_pie");
            fireworks = getRegisteredItem("fireworks");
            firework_charge = getRegisteredItem("firework_charge");
            enchanted_book = (ItemEnchantedBook) getRegisteredItem("enchanted_book");
            comparator = getRegisteredItem("comparator");
            netherbrick = getRegisteredItem("netherbrick");
            quartz = getRegisteredItem("quartz");
            tnt_minecart = getRegisteredItem("tnt_minecart");
            hopper_minecart = getRegisteredItem("hopper_minecart");
            armor_stand = (ItemArmorStand) getRegisteredItem("armor_stand");
            iron_horse_armor = getRegisteredItem("iron_horse_armor");
            golden_horse_armor = getRegisteredItem("golden_horse_armor");
            diamond_horse_armor = getRegisteredItem("diamond_horse_armor");
            lead = getRegisteredItem("lead");
            name_tag = getRegisteredItem("name_tag");
            command_block_minecart = getRegisteredItem("command_block_minecart");
            record_13 = getRegisteredItem("record_13");
            record_cat = getRegisteredItem("record_cat");
            record_blocks = getRegisteredItem("record_blocks");
            record_chirp = getRegisteredItem("record_chirp");
            record_far = getRegisteredItem("record_far");
            record_mall = getRegisteredItem("record_mall");
            record_mellohi = getRegisteredItem("record_mellohi");
            record_stal = getRegisteredItem("record_stal");
            record_strad = getRegisteredItem("record_strad");
            record_ward = getRegisteredItem("record_ward");
            record_11 = getRegisteredItem("record_11");
            record_wait = getRegisteredItem("record_wait");
            prismarine_shard = getRegisteredItem("prismarine_shard");
            prismarine_crystals = getRegisteredItem("prismarine_crystals");
            banner = getRegisteredItem("banner");
        }
    }
}
