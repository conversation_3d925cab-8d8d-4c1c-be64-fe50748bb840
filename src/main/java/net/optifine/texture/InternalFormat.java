package net.optifine.texture;

public enum InternalFormat {
    R8(33321),
    RG8(33323),
    <PERSON>GB8(32849),
    <PERSON><PERSON><PERSON><PERSON>(32856),
    R8_SNORM(36756),
    RG8_SNORM(36757),
    RGB8_SNORM(36758),
    RGBA8_SNORM(36759),
    <PERSON><PERSON>(33322),
    <PERSON><PERSON><PERSON>(33324),
    <PERSON><PERSON><PERSON>(32852),
    <PERSON><PERSON><PERSON><PERSON>(32859),
    R16_SNORM(36760),
    RG16_SNORM(36761),
    RGB16_SNORM(36762),
    RGBA16_SNORM(36763),
    R16<PERSON>(33325),
    RG16<PERSON>(33327),
    R<PERSON>16<PERSON>(34843),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(34842),
    R32<PERSON>(33326),
    RG32<PERSON>(33328),
    RGB32<PERSON>(34837),
    R<PERSON><PERSON>32F(34836),
    <PERSON>32<PERSON>(33333),
    R<PERSON>32<PERSON>(33339),
    R<PERSON>32<PERSON>(36227),
    RGBA32<PERSON>(36226),
    <PERSON><PERSON><PERSON>(33334),
    RG32<PERSON>(33340),
    <PERSON><PERSON><PERSON><PERSON>(36209),
    <PERSON><PERSON><PERSON><PERSON><PERSON>(36208),
    R3_G3_B2(10768),
    RGB5_A1(32855),
    <PERSON><PERSON>10_A2(32857),
    R11F_G11F_B10F(35898),
    RGB9_E5(35901);

    private int id;

    private InternalFormat(int id) {
        this.id = id;
    }

    public int getId() {
        return this.id;
    }
}
